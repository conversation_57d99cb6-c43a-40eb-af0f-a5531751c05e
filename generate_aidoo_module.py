import os
import shutil
from zipfile import ZipFile

def create_module(module_name, module_path):
    # Check if directory exists and prompt for overwrite
    if os.path.exists(module_path):
        response = input(f"Directory {module_path} already exists. Overwrite? (y/n): ")
        if response.lower() != 'y':
            print("Aborting.")
            return
        shutil.rmtree(module_path)
    
    os.makedirs(module_path, exist_ok=True)
    
    # Create __manifest__.py
    with open(os.path.join(module_path, '__manifest__.py'), 'w') as f:
        f.write(f"""\
{{
    'name': '{module_name}',
    'version': '1.0',
    'category': 'Custom',
    'summary': 'LLM Agent Integration with Open WebUI',
    'description': 'This module uses Open WebUI to translate AI requests into Odoo XML-RPC actions.',
    'depends': ['base'],
    'data': ['views/settings_view.xml'],
    'installable': True,
    'application': False,
}}
""")
    
    # Create models directory and settings.py
    models_path = os.path.join(module_path, 'models')
    os.makedirs(models_path, exist_ok=True)
    with open(os.path.join(models_path, 'settings.py'), 'w') as f:
        f.write("""\
from odoo import models, fields

class AidooSettings(models.Model):
    _name = 'aidoo.settings'
    _description = 'Aidoo Settings'

    api_url = fields.Char(string='Open WebUI API URL', required=True)
    api_key = fields.Char(string='API Key', required=True)
""")
    
    # Create controllers directory and main.py
    controllers_path = os.path.join(module_path, 'controllers')
    os.makedirs(controllers_path, exist_ok=True)
    with open(os.path.join(controllers_path, 'main.py'), 'w') as f:
        f.write("""\
from odoo import http
import requests
import json
import xmlrpc.client

class AidooController(http.Controller):
    @http.route('/aidoo/interpret', type='json', auth='user')
    def interpret_command(self, command):
        # Load Open WebUI settings
        settings = http.request.env['aidoo.settings'].sudo().search([], limit=1)
        if not settings:
            return {'error': 'Please configure Open WebUI settings'}
        
        # Send command to Open WebUI for interpretation
        api_url = settings.api_url
        api_key = settings.api_key
        headers = {'Authorization': f'Bearer {api_key}'}
        data = {'command': command}
        try:
            response = requests.post(api_url, headers=headers, json=data)
            response.raise_for_status()
            result = response.json()
        except Exception as e:
            return {'error': f'Failed to interpret command: {str(e)}'}
        
        # Translate result to XML-RPC call
        action = result.get('action')
        model = result.get('model')
        values = result.get('data', {})
        
        # XML-RPC configuration (should be secured/configurable in production)
        url = "http://localhost:8069"
        db = "mydatabase"
        username = "admin"
        password = "admin"
        
        common = xmlrpc.client.ServerProxy(f'{url}/xmlrpc/2/common')
        uid = common.authenticate(db, username, password, {})
        models = xmlrpc.client.ServerProxy(f'{url}/xmlrpc/2/object')
        
        try:
            if action == 'create':
                new_id = models.execute_kw(db, uid, password, model, 'create', [values])
                return {'status': 'success', 'id': new_id}
            elif action == 'read':
                records = models.execute_kw(db, uid, password, model, 'search_read', [[]])
                return {'status': 'success', 'records': records}
            else:
                return {'error': 'Unsupported action'}
        except Exception as e:
            return {'error': f'XML-RPC error: {str(e)}'}
""")
    
    # Create views directory and settings_view.xml
    views_path = os.path.join(module_path, 'views')
    os.makedirs(views_path, exist_ok=True)
    with open(os.path.join(views_path, 'settings_view.xml'), 'w') as f:
        f.write("""\
<odoo>
    <menuitem id="menu_aidoo_root" name="Aidoo" sequence="100"/>
    <record id="view_aidoo_settings_form" model="ir.ui.view">
        <field name="name">aidoo.settings.form</field>
        <field name="model">aidoo.settings</field>
        <field name="arch" type="xml">
            <form string="Aidoo Settings">
                <group>
                    <field name="api_url"/>
                    <field name="api_key" password="True"/>
                </group>
            </form>
        </field>
    </record>
    <record id="action_aidoo_settings" model="ir.actions.act_window">
        <field name="name">Aidoo Settings</field>
        <field name="res_model">aidoo.settings</field>
        <field name="view_mode">form</field>
        <field name="target">current</field>
    </record>
    <menuitem id="menu_aidoo_settings" name="Settings" parent="menu_aidoo_root" action="action_aidoo_settings"/>
</odoo>
""")
    
    # Create zip archive
    zip_name = "aidoo.zip"
    with ZipFile(zip_name, 'w') as zipf:
        for root, dirs, files in os.walk(module_path):
            for file in files:
                zipf.write(os.path.join(root, file), os.path.relpath(os.path.join(root, file), module_path))
    print(f"Module created in {module_path} and zipped as {zip_name}")

if __name__ == "__main__":
    module_name = "Aidoo LLM Agent"
    module_path = "./custom_modules/aidoo"
    create_module(module_name, module_path)