import sys
import base64
import re
from bs4 import BeautifulSoup  # You'll need to install BeautifulSoup: pip install beautifulsoup4

def extract_and_save_image(html_file_path):
    try:
        # Read the HTML file
        with open(html_file_path, 'r') as file:
            html_content = file.read()
        
        # Parse the HTML
        soup = BeautifulSoup(html_content, 'html.parser')
        
        # Find the first <img> tag with a data URL starting with data:image/png;base64,
        for img_tag in soup.find_all('img'):
            src = img_tag.get('src')
            if src and src.startswith('data:image/png;base64,'):
                # Extract the base64 part
                base64_data = src.split(',')[1]  # Split after the comma
                
                # Decode the base64 data
                image_data = base64.b64decode(base64_data)
                
                # Save to odoo_icon.png
                with open('odoo_icon.png', 'wb') as image_file:
                    image_file.write(image_data)
                
                print("Image successfully extracted and saved as odoo_icon.png")
                return  # Exit after first successful extraction
            
        print("No PNG data URL found in the HTML file.")
    except Exception as e:
        print(f"An error occurred: {e}")

if __name__ == '__main__':
    if len(sys.argv) != 2:
        print("Usage: python scripts/extract_image.py /path/to/your/htmlfile.html")
    else:
        extract_and_save_image(sys.argv[1])
