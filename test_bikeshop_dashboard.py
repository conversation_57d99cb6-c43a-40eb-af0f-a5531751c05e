#!/usr/bin/env python3
"""
Test script for Bikeshop Dashboard Button Configuration
This test analyzes the button configuration to identify why the "Neuer Kunde" button
is trying to access records with ID 'false' instead of opening a new contact form.
"""

import sys
import traceback

def analyze_button_configuration():
    """Analyze the button configuration to understand the issue"""

    print("🧪 Analyzing Bikeshop Dashboard Button Configuration...")

    # Read the current dashboard XML configuration
    try:
        with open('custom_addons/mfl/views/bikeshop_dashboard.xml', 'r') as f:
            dashboard_xml = f.read()

        print("📋 Current Dashboard Configuration:")
        print("=" * 50)
        print(dashboard_xml)
        print("=" * 50)

        # Analyze the button configuration
        print("\n📋 Button Analysis:")

        if 'base.action_partner_form' in dashboard_xml:
            print("✅ Found 'Neuer Kunde' button using base.action_partner_form")

            # Check the context
            if 'context=' in dashboard_xml:
                import re
                context_match = re.search(r'context="([^"]*)"', dashboard_xml)
                if context_match:
                    context = context_match.group(1)
                    print(f"📋 Button context: {context}")

                    # Analyze the context for issues
                    if 'view_mode' in context and 'form' in context:
                        print("✅ Context includes view_mode with form")
                    else:
                        print("❌ Context missing proper view_mode configuration")

                    if 'default_is_company' in context:
                        print("✅ Context includes default_is_company")
                    else:
                        print("❌ Context missing default_is_company")

                else:
                    print("❌ No context found in button configuration")
            else:
                print("❌ No context attribute found in button")

        else:
            print("❌ 'Neuer Kunde' button not using base.action_partner_form")

        # The issue analysis
        print("\n🔍 Issue Analysis:")
        print("The error 'records with IDs false not found' typically occurs when:")
        print("1. An action tries to open a specific record but gets 'false' as the ID")
        print("2. The action is configured for list/tree view but forced into form view")
        print("3. Missing proper context to create a new record")

        print("\n💡 Solution:")
        print("The button should use an action that directly opens a NEW record form, not an existing one.")
        print("We need to either:")
        print("1. Use a different action that's designed for creating new records")
        print("2. Modify the context to force creation mode")
        print("3. Use a custom action that explicitly creates a new record")

        return True

    except Exception as e:
        print(f"❌ Failed to analyze configuration: {e}")
        traceback.print_exc()
        return False

def test_button_fix():
    """Test the proposed fix for the button configuration"""

    print("\n🔧 Testing Button Fix...")

    # Proposed fix: Use a direct form action with proper context
    proposed_fix = '''
    <button string="Neuer Kunde"
            type="action"
            name="base.action_partner_form"
            class="btn-primary"
            context="{'default_is_company': False, 'form_view_initial_mode': 'edit'}"
            target="new"/>
    '''

    print("📋 Proposed Fix:")
    print(proposed_fix)

    print("\n📋 Key Changes:")
    print("1. ✅ Keep base.action_partner_form (it's the right action)")
    print("2. ✅ Add target='new' to open in a dialog/popup")
    print("3. ✅ Simplify context to essential parameters only")
    print("4. ✅ Remove view_mode from context (let action handle it)")

    return True
if __name__ == "__main__":
    print("🧪 Bikeshop Dashboard Button Analysis and Fix")
    print("=" * 60)

    # Step 1: Analyze current configuration
    analysis_success = analyze_button_configuration()

    # Step 2: Test the proposed fix
    if analysis_success:
        fix_success = test_button_fix()

        if fix_success:
            print("\n✅ Analysis complete! Ready to apply the fix.")
            sys.exit(0)
        else:
            print("\n❌ Fix testing failed!")
            sys.exit(1)
    else:
        print("\n❌ Configuration analysis failed!")
        sys.exit(1)


