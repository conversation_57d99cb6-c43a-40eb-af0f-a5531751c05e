services:
  odoo:
    image: odoo:18.0
    command: odoo -u mfl,aidoo --dev=all
    depends_on:
      - postgres
    ports:
      - "8069:8069"
    volumes:
      - odoo_data:/var/lib/odoo
      - ./custom_addons:/mnt/extra-addons
    environment:
      - HOST=postgres
      - USER=odoo
      - PASSWORD=secure_password_123
  postgres:
    image: postgres:17
    environment:
      - POSTGRES_USER=odoo
      - POSTGRES_PASSWORD=secure_password_123
      - POSTGRES_DB=postgres
    volumes:
      - postgres_data:/var/lib/postgresql/data
volumes:
  odoo_data:
  postgres_data:
