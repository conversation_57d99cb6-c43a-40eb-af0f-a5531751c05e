copy-item "ftb-xmod-compat-forge-2.1.1.jar" `
"aether_enhanced_extinguishing-1.20.1-1.0.0-neoforge.jar" `
"realmrpg_creep_and_crop_1.0.2_forge_1.20.1.jar" `
"cupboard-1.20.1-2.7.jar" `
"alexscaves_torpedoes-1.0.2.jar" `
"Paintings-forge-1.20.1-11.0.0.2.jar" `
"goblintraders-forge-1.20.1-1.9.3.jar" `
"transparent-forge-8.0.1+1.20.1.jar" `
"reliquary-1.20.1-2.0.41.1229.jar" `
"playershops-1.2.3-1.20.1.jar" `
"caelus-forge-3.2.0+1.20.1.jar" `
"Croptopia-1.20.1-FORGE-3.0.4.jar" `
"corpse-forge-1.20.1-1.0.14.jar" `
"Chimes-v2.0.1-1.20.1.jar" `
"CerbonsApi-Forge-1.20.1-1.0.0.jar" `
"realmrpg_imps_and_demons_0.9.0_forge_1.20.1.jar" `
"realmrpg_quests-0.1.1-forge-1.20.1.jar" `
"Paraglider-forge-20.1.3.jar" `
"MobsOfSins-v0.2.jar" `
"brutalbosses-1.20.1-7.1.jar" `
"framework-forge-1.20.1-0.7.8.jar" `
"dragonseeker-1.2.0-1.20.1.jar" `
"enemyexpansion-2.3.1-forge-1.20.1.jar" `
"waystones-forge-1.20-14.1.5.jar" `
"connectivity-1.20.1-5.6.jar" `
"ToastControl-1.20.1-8.0.3.jar" `
"sophisticatedbackpacks-1.20.1-3.20.6.1064.jar" `
"controllable-forge-1.20.1-0.20.3.jar" `
"realmrpg_dragon_wyrms_1.0.1_forge_1.20.1.jar" `
"common-networking-forge-1.0.5-1.20.1.jar" `
"inventoryhud.forge.1.20.1-3.4.26.jar" `
"BOMD-Forge-1.20.1-1.1.1.jar" `
"lootintegrations-1.20.1-3.7.jar" `
"despawn_tweaker-1.20.1-1.0.0.jar" `
"lootintegrationaddonyung-1.18-1.20.1-1.1.jar" `
"Clumps-forge-1.20.1-12.0.0.4.jar" `
"memorysettings-1.20.1-5.5.jar" `
"umbral_skies-1.3.jar" `
"citresewn-1.20.1-5.jar" `
"ecologics-forge-1.20.1-2.2.0.jar" `
"TaxCastlePillager+M.1.20.1+ForM.1.0.1.jar" `
"refurbished_furniture-forge-1.20.1-1.0.6.jar" `
"sereneseasonfix-1.20.1-1.0.8.jar" `
"KryptonReforged-0.2.3.jar" `
"dreadsteel-1.20.1-1.1.9.jar" `
"JustEnoughResources-1.20.1-1.4.0.247.jar" `
"Placebo-1.20.1-8.6.2.jar" `
"JustEnoughBeacons-Forge-1.19+-1.1.2.jar" `
"Controlling-forge-1.20.1-12.0.2.jar" `
"clickadv-1.20.1-3.8.jar" `
"twilightforest-1.20.1-4.3.2508-universal.jar" `
"justenoughbreeding-forge-1.20.x-1.4.0.jar" `
"ash_api-forge-3.0.2+1.20.1.jar" `
"Searchables-forge-1.20.1-1.0.3.jar" `
"worldplaytime-1.2.2-1.20.x-FORGE.jar" `
"FastSuite-1.20.1-5.0.1.jar" `
"seeds-1.20.1-1.1.5.jar" `
"ServerBrowser-1.20.1-FORGE-1.3.0.jar" `
"Raided-1.20.1-0.1.4.jar" `
"EpheroLib-1.20.1-FORGE-1.2.0.jar" `
'Nether''s Overhaul-1.20.1_V1.3.6.jar' `
"CerbonsBetterBeacons-Forge-1.20.1-1.1.1.jar" `
"JustEnoughProfessions-forge-1.20.1-3.0.1.jar" `
"ftb-quests-forge-2001.4.8.jar" `
"elevatorid-1.20.1-lex-1.9.jar" `
"ftb-teams-forge-2001.3.0.jar" `
"TerraBlender-forge-1.20.1-3.0.1.7.jar" `
"sophisticatedcore-1.20.1-0.6.25.632.jar" `
"bettervillage-forge-1.20.1-3.2.0.jar" `
"aether-1.20.1-1.4.2-neoforge.jar" `
"underground-jungle-forge-20.1.2.jar" `
"ftb-library-forge-2001.2.4.jar" `
"UndergroundVillages-forge-1.20.1-2.1.0.jar" `
"geckolib-forge-1.20.1-4.4.9.jar" `
"QueenBee-Forge-1.20.1-3.1.4.jar" `
"deuf-1.20.1-1.3.jar" `
"sophisticatedstorage-1.20.1-0.10.26.817.jar" `
"born_in_chaos_[Forge]1.20.1_1.3.1.jar" `
"gpumemleakfix-1.20.1-1.8.jar" `
"realmrpg_seadwellers_2.9.9_forge_1.20.1.jar" `
"L_Enders_Cataclysm-1.99.6-1.20.1.jar" `
"morevillagers-forge-1.20.1-5.0.0.jar" `
"lootintegrationaddondungeonsenhanced-1.17-1.20.1-1.1.jar" `
"balm-forge-1.20.1-7.3.9-all.jar" `
"slimy-stuff-1.20.1-1.1.1.jar" `
.\extra-mods