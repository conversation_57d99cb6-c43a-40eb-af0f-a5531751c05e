<odoo>
    <menuitem id="menu_aidoo_root" name="Aidoo" sequence="100"/>
    <record id="view_aidoo_settings_form" model="ir.ui.view">
        <field name="name">aidoo.settings.form</field>
        <field name="model">aidoo.settings</field>
        <field name="arch" type="xml">
            <form string="Aidoo Settings">
                <group>
                    <field name="api_url"/>
                    <field name="api_key" password="True"/>
                </group>
            </form>
        </field>
    </record>
    <record id="action_aidoo_settings" model="ir.actions.act_window">
        <field name="name">Aidoo Settings</field>
        <field name="res_model">aidoo.settings</field>
        <field name="view_mode">form</field>
        <field name="target">current</field>
    </record>
    <menuitem id="menu_aidoo_settings" name="Settings" parent="menu_aidoo_root" action="action_aidoo_settings"/>
</odoo>
