from odoo import http
import requests
import json
import xmlrpc.client

class <PERSON>oo<PERSON><PERSON>roller(http.Controller):
    @http.route('/aidoo/interpret', type='json', auth='user')
    def interpret_command(self, command):
        # Load Open WebUI settings
        settings = http.request.env['aidoo.settings'].sudo().search([], limit=1)
        if not settings:
            return {'error': 'Please configure Open WebUI settings'}
        
        # Send command to Open WebUI for interpretation
        api_url = settings.api_url
        api_key = settings.api_key
        headers = {'Authorization': f'Bearer {api_key}'}
        data = {'command': command}
        try:
            response = requests.post(api_url, headers=headers, json=data)
            response.raise_for_status()
            result = response.json()
        except Exception as e:
            return {'error': f'Failed to interpret command: {str(e)}'}
        
        # Translate result to XML-RPC call
        action = result.get('action')
        model = result.get('model')
        values = result.get('data', {})
        
        # XML-RPC configuration (should be secured/configurable in production)
        url = "http://localhost:8069"
        db = "mydatabase"
        username = "admin"
        password = "admin"
        
        common = xmlrpc.client.ServerProxy(f'{url}/xmlrpc/2/common')
        uid = common.authenticate(db, username, password, {})
        models = xmlrpc.client.ServerProxy(f'{url}/xmlrpc/2/object')
        
        try:
            if action == 'create':
                new_id = models.execute_kw(db, uid, password, model, 'create', [values])
                return {'status': 'success', 'id': new_id}
            elif action == 'read':
                records = models.execute_kw(db, uid, password, model, 'search_read', [[]])
                return {'status': 'success', 'records': records}
            else:
                return {'error': 'Unsupported action'}
        except Exception as e:
            return {'error': f'XML-RPC error: {str(e)}'}
