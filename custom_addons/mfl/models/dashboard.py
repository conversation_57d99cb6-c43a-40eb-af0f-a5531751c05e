from odoo import api, fields, models

class BikeshopDashboard(models.AbstractModel):
    _name = 'bikeshop.dashboard'
    _description = 'Bikeshop Dashboard'
    _auto = False

    # This field is necessary for when the web client opens a dashboard
    id = fields.Id()
    display_name = fields.Char(string='Display Name', default='<PERSON><PERSON>')

    @api.model_create_multi
    def create(self, vals_list):
        return self
